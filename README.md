# Student Attendance Tracker

A professional, clean, and complete attendance tracking system for schools built with HTML, JavaScript, and Tailwind CSS.

## Features

### 📚 Student Management
- Add new students with name, ID, class, and email
- Edit existing student information
- Delete students (with confirmation)
- Search students by name, ID, or class
- Unique student ID validation

### 📅 Attendance Tracking
- Mark attendance for any date
- Individual student attendance (Present/Absent)
- Bulk "Mark All Present" functionality
- Visual status indicators
- Date-based attendance records

### 📊 Reports & Analytics
- Real-time statistics dashboard
- Total students count
- Daily present/absent counts
- Detailed attendance reports with percentages
- Visual progress bars for attendance rates

### 💾 Data Persistence
- Automatic saving to browser localStorage
- Data persists between sessions
- No server required - works offline

### 📤 Export Functionality
- Export attendance data to CSV format
- Includes all student information and attendance records
- Downloadable reports for external use

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No additional software required

### Installation
1. Download all files to a folder
2. Open `index.html` in your web browser
3. Start using the attendance tracker immediately

### Running with a Local Server (Optional)
For better performance and testing:

```bash
# Using Node.js serve
npx serve .

# Using Python
python -m http.server 8000

# Using PHP
php -S localhost:8000
```

## Usage Guide

### Adding Students
1. Click the "Students" tab
2. Click "Add Student" button
3. Fill in student information:
   - Full Name (required)
   - Student ID (required, must be unique)
   - Class (required)
   - Email (optional)
4. Click "Save Student"

### Managing Students
- **Edit**: Click the edit icon next to any student
- **Delete**: Click the trash icon (requires confirmation)
- **Search**: Use the search bar to find students quickly

### Marking Attendance
1. Click the "Attendance" tab
2. Select the date (defaults to today)
3. For each student, click either:
   - "Present" button (green)
   - "Absent" button (red)
4. Use "Mark All Present" for quick bulk marking

### Viewing Reports
1. Click the "Reports" tab
2. View real-time statistics at the top
3. Scroll down for detailed attendance report
4. See attendance rates and progress bars

### Exporting Data
1. Click the "Export" button in the header
2. CSV file will download automatically
3. File includes all student and attendance data

## File Structure

```
attendance-tracker/
├── index.html          # Main application file
├── app.js             # JavaScript functionality
├── test.html          # Simple test file
└── README.md          # This documentation
```

## Technical Details

### Technologies Used
- **HTML5**: Semantic structure and accessibility
- **Tailwind CSS**: Professional styling and responsive design
- **Vanilla JavaScript**: Core functionality and data management
- **Lucide Icons**: Modern, clean iconography
- **localStorage**: Client-side data persistence

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Data Storage
- All data stored in browser's localStorage
- No external database required
- Data persists until manually cleared
- Automatic backup on every change

## Features in Detail

### Professional Design
- Clean, modern interface
- Responsive design for all devices
- Intuitive navigation with tabs
- Visual feedback for all actions
- Professional color scheme

### Data Validation
- Required field validation
- Unique student ID enforcement
- Email format validation (when provided)
- Confirmation dialogs for destructive actions

### User Experience
- Smooth animations and transitions
- Real-time search and filtering
- Instant feedback notifications
- Keyboard-friendly interface
- Mobile-responsive design

## Troubleshooting

### Common Issues

**Data not saving:**
- Ensure localStorage is enabled in your browser
- Check if you're in private/incognito mode
- Clear browser cache and try again

**Export not working:**
- Ensure pop-ups are allowed for the site
- Check browser download settings
- Try a different browser

**Students not appearing:**
- Check if any search filters are applied
- Refresh the page
- Clear localStorage and re-add students

### Browser Storage Limits
- localStorage typically allows 5-10MB per domain
- This tracker can handle thousands of students and attendance records
- If you reach limits, export data and clear old records

## Support

For issues or questions:
1. Check this README for common solutions
2. Verify browser compatibility
3. Test with a fresh browser session
4. Check browser console for error messages

## License

This project is open source and available under the MIT License.

---

**Built with ❤️ for educational institutions**
