// Student Attendance Tracker Application
class AttendanceTracker {
    constructor() {
        this.students = JSON.parse(localStorage.getItem('students')) || [];
        this.attendance = JSON.parse(localStorage.getItem('attendance')) || {};
        this.currentEditingStudent = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateCurrentDate();
        this.populateYearSelector();
        this.setCurrentMonthYear();
        this.renderStudents();
        this.renderAttendanceGrid();
        this.updateStats();
        this.restoreActiveTab(); // Restore the last active tab
        lucide.createIcons();
    }

    setupEventListeners() {
        // Tab navigation
        document.getElementById('studentsTab').addEventListener('click', () => this.showTab('students'));
        document.getElementById('attendanceTab').addEventListener('click', () => this.showTab('attendance'));
        document.getElementById('reportsTab').addEventListener('click', () => this.showTab('reports'));

        // Student management
        document.getElementById('addStudentBtn').addEventListener('click', () => this.showStudentModal());
        document.getElementById('studentForm').addEventListener('submit', (e) => this.handleStudentSubmit(e));
        document.getElementById('cancelBtn').addEventListener('click', () => this.hideStudentModal());
        document.getElementById('searchStudents').addEventListener('input', (e) => this.searchStudents(e.target.value));

        // Attendance
        document.getElementById('monthSelector').addEventListener('change', () => this.renderAttendanceGrid());
        document.getElementById('yearSelector').addEventListener('change', () => this.renderAttendanceGrid());

        // Export
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());

        // Modal close on outside click
        document.getElementById('studentModal').addEventListener('click', (e) => {
            if (e.target.id === 'studentModal') this.hideStudentModal();
        });
    }

    updateCurrentDate() {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', options);
    }

    populateYearSelector() {
        const yearSelector = document.getElementById('yearSelector');
        const currentYear = new Date().getFullYear();

        // Add years from 2020 to current year + 2
        for (let year = 2020; year <= currentYear + 2; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            if (year === currentYear) option.selected = true;
            yearSelector.appendChild(option);
        }
    }

    setCurrentMonthYear() {
        const now = new Date();
        document.getElementById('monthSelector').value = now.getMonth();
        document.getElementById('yearSelector').value = now.getFullYear();
    }

    showTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active', 'border-blue-600', 'text-blue-600');
            btn.classList.add('border-transparent', 'text-gray-500');
        });

        document.getElementById(`${tabName}Tab`).classList.add('active', 'border-blue-600', 'text-blue-600');
        document.getElementById(`${tabName}Tab`).classList.remove('border-transparent', 'text-gray-500');

        // Show/hide panels
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.add('hidden'));
        document.getElementById(`${tabName}Panel`).classList.remove('hidden');

        // Save the current active tab to localStorage
        localStorage.setItem('activeTab', tabName);

        if (tabName === 'reports') {
            this.updateStats();
            this.generateAttendanceReport();
        } else if (tabName === 'attendance') {
            this.renderAttendanceGrid();
        }
    }

    restoreActiveTab() {
        // Get the saved active tab from localStorage, default to 'students'
        const savedTab = localStorage.getItem('activeTab') || 'students';
        this.showTab(savedTab);
    }

    showStudentModal(student = null) {
        this.currentEditingStudent = student;
        const modal = document.getElementById('studentModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('studentForm');

        if (student) {
            title.textContent = 'Edit Student';
            document.getElementById('studentName').value = student.name;
            document.getElementById('studentId').value = student.id;
            document.getElementById('studentClass').value = student.class;
            document.getElementById('studentEmail').value = student.email || '';
        } else {
            title.textContent = 'Add Student';
            form.reset();
        }

        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    hideStudentModal() {
        document.getElementById('studentModal').classList.add('hidden');
        document.getElementById('studentModal').classList.remove('flex');
        this.currentEditingStudent = null;
    }

    handleStudentSubmit(e) {
        e.preventDefault();
        
        const studentData = {
            name: document.getElementById('studentName').value.trim(),
            id: document.getElementById('studentId').value.trim(),
            class: document.getElementById('studentClass').value.trim(),
            email: document.getElementById('studentEmail').value.trim(),
            dateAdded: new Date().toISOString()
        };

        // Validate unique student ID
        const existingStudent = this.students.find(s => s.id === studentData.id && s !== this.currentEditingStudent);
        if (existingStudent) {
            alert('Student ID already exists. Please use a unique ID.');
            return;
        }

        if (this.currentEditingStudent) {
            // Update existing student
            const index = this.students.findIndex(s => s === this.currentEditingStudent);
            this.students[index] = { ...this.currentEditingStudent, ...studentData };
        } else {
            // Add new student
            studentData.uuid = this.generateUUID();
            this.students.push(studentData);
        }

        this.saveData();
        this.renderStudents();
        this.renderAttendanceGrid();

        // If we're on the reports tab, refresh the reports too
        const activeTab = localStorage.getItem('activeTab');
        if (activeTab === 'reports') {
            this.generateAttendanceReport();
        }

        this.hideStudentModal();
        this.showNotification(this.currentEditingStudent ? 'Student updated successfully!' : 'Student added successfully!');
    }

    deleteStudent(student) {
        if (confirm(`Are you sure you want to delete ${student.name}? This will also remove all attendance records.`)) {
            this.students = this.students.filter(s => s !== student);
            
            // Remove attendance records for this student
            Object.keys(this.attendance).forEach(date => {
                delete this.attendance[date][student.uuid];
            });

            this.saveData();
            this.renderStudents();
            this.renderAttendanceGrid();

            // If we're on the reports tab, refresh the reports too
            const activeTab = localStorage.getItem('activeTab');
            if (activeTab === 'reports') {
                this.generateAttendanceReport();
            }

            this.showNotification('Student deleted successfully!');
        }
    }

    searchStudents(query) {
        const filteredStudents = this.students.filter(student => 
            student.name.toLowerCase().includes(query.toLowerCase()) ||
            student.id.toLowerCase().includes(query.toLowerCase()) ||
            student.class.toLowerCase().includes(query.toLowerCase())
        );
        this.renderStudents(filteredStudents);
    }

    renderStudents(studentsToRender = this.students) {
        const container = document.getElementById('studentsList');
        const noStudents = document.getElementById('noStudents');

        if (studentsToRender.length === 0) {
            container.innerHTML = '';
            noStudents.classList.remove('hidden');
            return;
        }

        noStudents.classList.add('hidden');
        container.innerHTML = studentsToRender.map(student => `
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors fade-in">
                <div class="flex items-center space-x-4">
                    <div class="bg-blue-100 p-2 rounded-full">
                        <i data-lucide="user" class="w-5 h-5 text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">${student.name}</h3>
                        <p class="text-sm text-gray-600">ID: ${student.id} • Teacher: ${student.class}</p>
                        ${student.email ? `<p class="text-sm text-gray-500">${student.email}</p>` : ''}
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="app.showStudentModal(app.students.find(s => s.uuid === '${student.uuid}'))" 
                            class="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                        <i data-lucide="edit" class="w-4 h-4"></i>
                    </button>
                    <button onclick="app.deleteStudent(app.students.find(s => s.uuid === '${student.uuid}'))" 
                            class="p-2 text-gray-400 hover:text-red-600 transition-colors">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        `).join('');

        lucide.createIcons();
    }

    renderAttendanceGrid() {
        const container = document.getElementById('attendanceGrid');
        const noStudents = document.getElementById('noStudentsForAttendance');

        if (this.students.length === 0) {
            container.innerHTML = '';
            noStudents.classList.remove('hidden');
            return;
        }

        noStudents.classList.add('hidden');

        const selectedMonth = parseInt(document.getElementById('monthSelector').value);
        const selectedYear = parseInt(document.getElementById('yearSelector').value);
        const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();

        // Create header
        let gridHTML = `
            <table class="attendance-table border-collapse">
                <thead>
                    <tr>
                        <th style="width: 120px;">Student ID</th>
                        <th style="width: 200px;">Student Name</th>
                        <th style="width: 150px;">Teacher Name</th>`;

        // Add day headers
        for (let day = 1; day <= daysInMonth; day++) {
            gridHTML += `<th class="checkbox-cell">${day}</th>`;
        }

        gridHTML += `
                        <th style="background: #10B981; color: white; width: 100px;">Present</th>
                        <th style="background: #EF4444; color: white; width: 100px;">Absent</th>
                        <th style="background: #3B82F6; color: white; width: 100px;">Total Days</th>
                        <th style="background: #F59E0B; color: white; width: 120px;">Attendance %</th>
                    </tr>
                </thead>
                <tbody>`;

        // Add student rows
        this.students.forEach((student) => {
            gridHTML += `<tr>`;
            gridHTML += `<td class="student-info-cell" style="font-weight: 500;">${student.id}</td>`;
            gridHTML += `<td class="student-info-cell" style="font-weight: 500;">${student.name}</td>`;
            gridHTML += `<td class="student-info-cell">${student.class}</td>`;

            let presentDays = 0;
            let absentDays = 0;
            let totalMarkedDays = 0;

            // Add checkboxes for each day
            for (let day = 1; day <= daysInMonth; day++) {
                const dateStr = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const status = this.attendance[dateStr] ? this.attendance[dateStr][student.uuid] : undefined;
                const isPresent = status === 'present';
                const isAbsent = status === 'absent';

                if (isPresent) presentDays++;
                if (isAbsent) absentDays++;
                if (isPresent || isAbsent) totalMarkedDays++; // Count both present and absent as marked

                let cellContent = '';
                if (isPresent) {
                    cellContent = `<span style="color: #10B981; font-size: 16px; font-weight: bold;">✓</span>`;
                } else if (isAbsent) {
                    cellContent = `<span style="color: #EF4444; font-size: 16px; font-weight: bold;">✗</span>`;
                } else {
                    cellContent = `<span style="color: #9CA3AF; font-size: 12px;">-</span>`;
                }

                gridHTML += `
                    <td class="checkbox-cell"
                        onclick="app.toggleAttendance('${student.uuid}', '${dateStr}')"
                        oncontextmenu="app.unmarkAttendance('${student.uuid}', '${dateStr}'); return false;"
                        title="${status ? (isPresent ? 'Present - Click to mark Absent, Right-click to Unmark' : 'Absent - Click to Unmark, Right-click to Unmark') : 'Not Marked - Click to mark Present, Right-click to Unmark'}"
                        style="cursor: pointer; background-color: ${isPresent ? '#F0FDF4' : isAbsent ? '#FEF2F2' : '#FFFFFF'};">
                        ${cellContent}
                    </td>`;
            }

            const attendancePercentage = totalMarkedDays > 0 ? Math.round((presentDays / totalMarkedDays) * 100) : 0;

            // Debug for first student only
            if (student === this.students[0]) {
                console.log(`${student.name}: Present=${presentDays}, Marked=${totalMarkedDays}, %=${attendancePercentage}`);
            }

            // Color code the attendance percentage
            let percentageColor = '#DC2626'; // Red for low attendance
            if (attendancePercentage >= 80) percentageColor = '#10B981'; // Green for good attendance
            else if (attendancePercentage >= 60) percentageColor = '#F59E0B'; // Orange for medium attendance

            gridHTML += `
                <td class="stats-cell" style="color: #10B981;">${presentDays}</td>
                <td class="stats-cell" style="color: #EF4444;">${absentDays}</td>
                <td class="stats-cell" style="color: #3B82F6;">${totalMarkedDays}</td>
                <td class="stats-cell" style="color: ${percentageColor};">${attendancePercentage}%</td>
            </tr>`;
        });

        gridHTML += '</tbody></table>';
        container.innerHTML = gridHTML;
    }

    toggleAttendance(studentUuid, dateStr) {
        if (!this.attendance[dateStr]) {
            this.attendance[dateStr] = {};
        }

        // Cycle through: not marked -> present -> absent -> not marked
        const currentStatus = this.attendance[dateStr][studentUuid];
        if (!currentStatus) {
            this.attendance[dateStr][studentUuid] = 'present';
        } else if (currentStatus === 'present') {
            this.attendance[dateStr][studentUuid] = 'absent';
        } else {
            delete this.attendance[dateStr][studentUuid];

            // Clean up empty date objects
            if (Object.keys(this.attendance[dateStr]).length === 0) {
                delete this.attendance[dateStr];
            }
        }

        this.saveData();
        this.renderAttendanceGrid();
        this.updateStats();

        // If we're on the reports tab, refresh the reports too
        const activeTab = localStorage.getItem('activeTab');
        if (activeTab === 'reports') {
            this.generateAttendanceReport();
        }
    }

    unmarkAttendance(studentUuid, dateStr) {
        if (this.attendance[dateStr] && this.attendance[dateStr][studentUuid]) {
            delete this.attendance[dateStr][studentUuid];

            // Clean up empty date objects
            if (Object.keys(this.attendance[dateStr]).length === 0) {
                delete this.attendance[dateStr];
            }

            this.saveData();
            this.renderAttendanceGrid();
            this.updateStats();

            // If we're on the reports tab, refresh the reports too
            const activeTab = localStorage.getItem('activeTab');
            if (activeTab === 'reports') {
                this.generateAttendanceReport();
            }

            this.showNotification('Attendance unmarked', 'success');
        }
    }

    markAttendance(studentUuid, status) {
        const selectedMonth = parseInt(document.getElementById('monthSelector').value);
        const selectedYear = parseInt(document.getElementById('yearSelector').value);
        const today = new Date();
        const dateStr = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

        if (!this.attendance[dateStr]) {
            this.attendance[dateStr] = {};
        }

        this.attendance[dateStr][studentUuid] = status;
        this.saveData();
        this.renderAttendanceGrid();
        this.updateStats();

        // If we're on the reports tab, refresh the reports too
        const activeTab = localStorage.getItem('activeTab');
        if (activeTab === 'reports') {
            this.generateAttendanceReport();
        }
    }

    markAllPresent() {
        const selectedMonth = parseInt(document.getElementById('monthSelector').value);
        const selectedYear = parseInt(document.getElementById('yearSelector').value);
        const today = new Date();
        const dateStr = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

        if (!this.attendance[dateStr]) {
            this.attendance[dateStr] = {};
        }

        this.students.forEach(student => {
            this.attendance[dateStr][student.uuid] = 'present';
        });

        this.saveData();
        this.renderAttendanceGrid();
        this.updateStats();

        // If we're on the reports tab, refresh the reports too
        const activeTab = localStorage.getItem('activeTab');
        if (activeTab === 'reports') {
            this.generateAttendanceReport();
        }

        this.showNotification('All students marked present for today!');
    }

    updateStats() {
        const today = new Date().toISOString().split('T')[0];
        const todayAttendance = this.attendance[today] || {};
        
        const totalStudents = this.students.length;
        const presentToday = Object.values(todayAttendance).filter(status => status === 'present').length;
        const absentToday = Object.values(todayAttendance).filter(status => status === 'absent').length;

        document.getElementById('totalStudents').textContent = totalStudents;
        document.getElementById('presentToday').textContent = presentToday;
        document.getElementById('absentToday').textContent = absentToday;
    }

    generateAttendanceReport() {
        const container = document.getElementById('attendanceReport');
        
        if (this.students.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-8">No students available for report.</p>';
            return;
        }

        const reportData = this.students.map(student => {
            const attendanceRecords = Object.entries(this.attendance).map(([date, records]) => ({
                date,
                status: records[student.uuid] || 'not-marked'
            }));

            const totalDays = attendanceRecords.length;
            const presentDays = attendanceRecords.filter(record => record.status === 'present').length;
            const absentDays = attendanceRecords.filter(record => record.status === 'absent').length;
            const markedDays = presentDays + absentDays; // Only count days where student was actually marked
            const attendanceRate = markedDays > 0 ? Math.round((presentDays / markedDays) * 100) : 0;

            return {
                student,
                totalDays,
                presentDays,
                absentDays,
                markedDays,
                attendanceRate
            };
        });

        container.innerHTML = `
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marked Days</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance Rate</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${reportData.map(data => `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">${data.student.name}</div>
                                        <div class="text-sm text-gray-500">ID: ${data.student.id}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${data.markedDays}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">${data.presentDays}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">${data.absentDays}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900">${data.attendanceRate}%</div>
                                        <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: ${data.attendanceRate}%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    exportData() {
        const selectedMonth = parseInt(document.getElementById('monthSelector').value);
        const selectedYear = parseInt(document.getElementById('yearSelector').value);
        const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
        const monthName = new Date(selectedYear, selectedMonth).toLocaleString('default', { month: 'long' });

        // Create Excel-compatible XML format
        let xmlContent = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <Styles>
  <Style ss:ID="Title">
   <Interior ss:Color="#1E40AF" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#FFFFFF" ss:Size="16"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="2"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="SubTitle">
   <Interior ss:Color="#3B82F6" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#FFFFFF" ss:Size="12"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="Header">
   <Interior ss:Color="#4F46E5" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#FFFFFF" ss:Size="11"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="StudentInfo">
   <Interior ss:Color="#E5E7EB" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#374151"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="Present">
   <Interior ss:Color="#DCFCE7" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#15803D" ss:Size="12"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="Absent">
   <Interior ss:Color="#FEE2E2" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#B91C1C" ss:Size="12"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="NotMarked">
   <Interior ss:Color="#F9FAFB" ss:Pattern="Solid"/>
   <Font ss:Color="#9CA3AF" ss:Size="11"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="Stats">
   <Interior ss:Color="#DBEAFE" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#1E40AF"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="Legend">
   <Interior ss:Color="#F3F4F6" ss:Pattern="Solid"/>
   <Font ss:Bold="1" ss:Color="#4B5563" ss:Size="10"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Attendance ${monthName} ${selectedYear}">
  <Table>
   <Row ss:Height="30">
    <Cell ss:StyleID="Title" ss:MergeAcross="${daysInMonth + 6}"><Data ss:Type="String">STUDENTS ATTENDANCE TRACKER</Data></Cell>
   </Row>
   <Row ss:Height="25">
    <Cell ss:StyleID="SubTitle" ss:MergeAcross="${daysInMonth + 6}"><Data ss:Type="String">${monthName} ${selectedYear} - Monthly Attendance Report</Data></Cell>
   </Row>
   <Row ss:Height="5">
    <Cell ss:MergeAcross="${daysInMonth + 6}"><Data ss:Type="String"></Data></Cell>
   </Row>
   <Row ss:Height="20">
    <Cell ss:StyleID="Legend"><Data ss:Type="String">✓ Present</Data></Cell>
    <Cell ss:StyleID="Legend"><Data ss:Type="String">✗ Absent</Data></Cell>
    <Cell ss:StyleID="Legend"><Data ss:Type="String">- Not Marked</Data></Cell>
    <Cell ss:MergeAcross="${daysInMonth + 3}"><Data ss:Type="String"></Data></Cell>
   </Row>
   <Row ss:Height="5">
    <Cell ss:MergeAcross="${daysInMonth + 6}"><Data ss:Type="String"></Data></Cell>
   </Row>
   <Row ss:Height="25">
    <Cell ss:StyleID="Header"><Data ss:Type="String">Student ID</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Student Name</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Teacher Name</Data></Cell>`;

        // Add day headers
        for (let day = 1; day <= daysInMonth; day++) {
            xmlContent += `<Cell ss:StyleID="Header"><Data ss:Type="Number">${day}</Data></Cell>`;
        }
        xmlContent += `<Cell ss:StyleID="Header"><Data ss:Type="String">Present</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Absent</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Total Days</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Attendance %</Data></Cell>
   </Row>`;

        // Add student rows
        this.students.forEach(student => {
            xmlContent += `<Row ss:Height="22">
    <Cell ss:StyleID="StudentInfo"><Data ss:Type="String">${student.id}</Data></Cell>
    <Cell ss:StyleID="StudentInfo"><Data ss:Type="String">${student.name}</Data></Cell>
    <Cell ss:StyleID="StudentInfo"><Data ss:Type="String">${student.class}</Data></Cell>`;

            let presentDays = 0;
            let absentDays = 0;
            let totalMarkedDays = 0;

            // Add attendance for each day
            for (let day = 1; day <= daysInMonth; day++) {
                const dateStr = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const status = this.attendance[dateStr] ? this.attendance[dateStr][student.uuid] : undefined;
                const isPresent = status === 'present';
                const isAbsent = status === 'absent';

                if (isPresent) {
                    xmlContent += `<Cell ss:StyleID="Present"><Data ss:Type="String">✓</Data></Cell>`;
                    presentDays++;
                    totalMarkedDays++;
                } else if (isAbsent) {
                    xmlContent += `<Cell ss:StyleID="Absent"><Data ss:Type="String">✗</Data></Cell>`;
                    absentDays++;
                    totalMarkedDays++;
                } else {
                    xmlContent += `<Cell ss:StyleID="NotMarked"><Data ss:Type="String">-</Data></Cell>`;
                }
            }

            // Add summary columns
            const attendancePercentage = totalMarkedDays > 0 ? Math.round((presentDays / totalMarkedDays) * 100) : 0;
            xmlContent += `<Cell ss:StyleID="Stats"><Data ss:Type="Number">${presentDays}</Data></Cell>
    <Cell ss:StyleID="Stats"><Data ss:Type="Number">${absentDays}</Data></Cell>
    <Cell ss:StyleID="Stats"><Data ss:Type="Number">${totalMarkedDays}</Data></Cell>
    <Cell ss:StyleID="Stats"><Data ss:Type="String">${attendancePercentage}%</Data></Cell>
   </Row>`;
        });

        xmlContent += `</Table>
 </Worksheet>
</Workbook>`;

        // Download as Excel file
        const blob = new Blob([xmlContent], { type: 'application/vnd.ms-excel' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `attendance-${monthName}-${selectedYear}.xls`;
        a.click();
        window.URL.revokeObjectURL(url);

        this.showNotification('Excel attendance sheet exported successfully!');
    }

    saveData() {
        localStorage.setItem('students', JSON.stringify(this.students));
        localStorage.setItem('attendance', JSON.stringify(this.attendance));
    }

    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    showNotification(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 fade-in';
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the application
const app = new AttendanceTracker();
