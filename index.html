<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Attendance Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Excel-style table styling */
        .attendance-table {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            width: 100%;
            table-layout: fixed;
        }

        .attendance-table th {
            background: linear-gradient(to bottom, #4F46E5, #3730A3);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 12px 8px;
            border: 1px solid #E5E7EB;
            white-space: nowrap;
        }

        .attendance-table td {
            border: 1px solid #E5E7EB;
            padding: 8px 6px;
            text-align: center;
            vertical-align: middle;
        }

        .attendance-table tr:nth-child(even) {
            background-color: #F9FAFB;
        }

        .attendance-table tr:hover {
            background-color: #EBF4FF;
        }

        .day-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 25px;
            max-width: 25px;
        }

        .checkbox-cell {
            width: 35px;
            padding: 8px 4px;
            transition: all 0.2s ease;
        }

        .checkbox-cell:hover {
            background-color: #E0E7FF !important;
            transform: scale(1.1);
        }

        .attendance-checkbox {
            transform: scale(1.3);
            accent-color: #10B981;
            cursor: pointer;
        }

        .student-info-cell {
            text-align: left;
            padding: 12px 16px;
        }

        .stats-cell {
            padding: 12px 16px;
            font-weight: 600;
            min-width: 80px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="bg-blue-600 p-2 rounded-lg">
                        <i data-lucide="users" class="w-6 h-6 text-white"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900">Student Attendance Tracker</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="currentDate" class="text-sm text-gray-600"></span>
                    <button id="exportBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <!-- Navigation Tabs -->
        <div class="mb-8">
            <nav class="flex space-x-8">
                <button id="studentsTab" class="tab-button active px-3 py-2 text-sm font-medium border-b-2 border-blue-600 text-blue-600">
                    Students
                </button>
                <button id="attendanceTab" class="tab-button px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    Attendance
                </button>
                <button id="reportsTab" class="tab-button px-3 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    Reports
                </button>
            </nav>
        </div>

        <!-- Students Tab -->
        <div id="studentsPanel" class="tab-panel">
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">Student Management</h2>
                        <button id="addStudentBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                            <span>Add Student</span>
                        </button>
                    </div>
                    
                    <!-- Search Bar -->
                    <div class="relative">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        <input type="text" id="searchStudents" placeholder="Search students..." 
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
                
                <!-- Students List -->
                <div class="p-6">
                    <div id="studentsList" class="space-y-3">
                        <!-- Students will be dynamically loaded here -->
                    </div>
                    <div id="noStudents" class="text-center py-12 text-gray-500 hidden">
                        <i data-lucide="users" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                        <p>No students added yet. Click "Add Student" to get started.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Tab -->
        <div id="attendancePanel" class="tab-panel hidden">
            <div class="bg-white rounded-lg shadow-sm border w-full">
                <!-- Header with Month/Year Controls -->
                <div class="p-6 border-b bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-bold">Attendance Sheet</h2>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium">Month:</label>
                                <select id="monthSelector" class="bg-white text-gray-900 border border-gray-300 rounded px-3 py-1 text-sm">
                                    <option value="0">January</option>
                                    <option value="1">February</option>
                                    <option value="2">March</option>
                                    <option value="3">April</option>
                                    <option value="4">May</option>
                                    <option value="5">June</option>
                                    <option value="6">July</option>
                                    <option value="7">August</option>
                                    <option value="8">September</option>
                                    <option value="9">October</option>
                                    <option value="10">November</option>
                                    <option value="11">December</option>
                                </select>
                            </div>
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium">Year:</label>
                                <select id="yearSelector" class="bg-white text-gray-900 border border-gray-300 rounded px-3 py-1 text-sm">
                                    <!-- Years will be populated by JavaScript -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div class="px-6 py-3 bg-gray-50 border-b">
                    <div class="flex items-center justify-center space-x-8 text-sm">
                        <div class="flex items-center space-x-2">
                            <span style="color: #10B981; font-size: 16px; font-weight: bold;">✓</span>
                            <span class="text-gray-700">Present</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span style="color: #EF4444; font-size: 16px; font-weight: bold;">✗</span>
                            <span class="text-gray-700">Absent</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span style="color: #9CA3AF; font-size: 12px;">-</span>
                            <span class="text-gray-700">Not Marked</span>
                        </div>
                        <div class="text-gray-600 italic">
                            <strong>Left-click:</strong> Cycle through Not Marked → Present → Absent → Not Marked<br>
                            <strong>Right-click:</strong> Instantly unmark (clear attendance)
                        </div>
                    </div>
                </div>

                <!-- Attendance Grid -->
                <div class="overflow-x-auto w-full">
                    <div id="attendanceGrid" class="w-full">
                        <!-- Grid will be dynamically generated here -->
                    </div>
                    <div id="noStudentsForAttendance" class="text-center py-12 text-gray-500 hidden">
                        <i data-lucide="calendar-x" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                        <p>No students available. Please add students first.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Tab -->
        <div id="reportsPanel" class="tab-panel hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- Stats Cards -->
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-3 rounded-lg">
                            <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Students</p>
                            <p id="totalStudents" class="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="flex items-center">
                        <div class="bg-green-100 p-3 rounded-lg">
                            <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Present Today</p>
                            <p id="presentToday" class="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-sm border">
                    <div class="flex items-center">
                        <div class="bg-red-100 p-3 rounded-lg">
                            <i data-lucide="x-circle" class="w-6 h-6 text-red-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Absent Today</p>
                            <p id="absentToday" class="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Report -->
            <div class="bg-white rounded-lg shadow-sm border">
                <div class="p-6 border-b">
                    <h2 class="text-xl font-semibold text-gray-900">Attendance Report</h2>
                </div>
                <div class="p-6">
                    <div id="attendanceReport">
                        <!-- Report will be generated here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Student Modal -->
    <div id="studentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6 border-b">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add Student</h3>
            </div>
            <form id="studentForm" class="p-6">
                <div class="space-y-4">
                    <div>
                        <label for="studentName" class="block text-sm font-medium text-gray-700 mb-1">Full Name:</label>
                        <input type="text" id="studentName" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="studentId" class="block text-sm font-medium text-gray-700 mb-1">Student ID:</label>
                        <input type="text" id="studentId" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="studentClass" class="block text-sm font-medium text-gray-700 mb-1">Teacher Name:</label>
                        <input type="text" id="studentClass" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="studentEmail" class="block text-sm font-medium text-gray-700 mb-1">Email (Optional)</label>
                        <input type="email" id="studentEmail" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" id="cancelBtn" class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        Save Student
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
