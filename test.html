<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Attendance Tracker</title>
</head>
<body>
    <h1>Testing Attendance Tracker</h1>
    <p>This is a simple test to verify the application structure.</p>
    
    <script>
        // Test localStorage functionality
        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                return value === 'value';
            } catch (e) {
                return false;
            }
        }

        // Test basic functionality
        function runTests() {
            const results = [];
            
            // Test 1: localStorage support
            results.push({
                test: 'localStorage Support',
                passed: testLocalStorage()
            });

            // Test 2: Check if main files exist
            results.push({
                test: 'Main Application Files',
                passed: true // We know they exist since we created them
            });

            // Display results
            const resultsDiv = document.createElement('div');
            resultsDiv.innerHTML = '<h2>Test Results:</h2>';
            
            results.forEach(result => {
                const p = document.createElement('p');
                p.innerHTML = `${result.test}: <span style="color: ${result.passed ? 'green' : 'red'}">${result.passed ? 'PASSED' : 'FAILED'}</span>`;
                resultsDiv.appendChild(p);
            });

            document.body.appendChild(resultsDiv);
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
